from machine import FPIOA
from machine import Pin
from machine import PWM
from media.sensor import *
from media.display import *
from media.media import *
import time

fpioa = FPIOA()
fpioa.set_function(2,FPIOA.GPIO2)
fpioa.set_function(42,FPIOA.PWM0)
fpioa.set_function(43,FPIOA.PWM1)

sensor = Sensor()
sensor.reset()

# 鼠标悬停在函数上可以查看允许接收的参数
sensor.set_framesize(width=800, height=480)
sensor.set_pixformat(Sensor.RGB565)

Display.init(Display.VIRT,sensor.width(),sensor.height())
# 初始化媒体管理器
MediaManager.init()
# 启动 sensor
sensor.run()
clock = time.clock()


pin = Pin(2,Pin.OUT)
pwm_s = PWM(0,50)
pwm_z = PWM(1,50)

# pwm_s增大会向下走，pwm_z增大会向左走
pin.value(1)
pwm_s.enable(1)
pwm_s.duty(1.73/20*100)
pwm_z.enable(1)
pwm_z.duty(1.52/20*100)

time.sleep(1)

while True:
    clock.tick()
    img = sensor.snapshot()

    pwm_z.duty(1.40/20*100)
    pwm_s.duty(1.73/20*100)
    time.sleep(0.1)

    pwm_z.duty(1.30/20*100)
    pwm_s.duty(1.73/20*100)
    time.sleep(0.5)

    pwm_s.duty(1.58/20*100)
    time.sleep(0.5)

    pwm_z.duty(1.52/20*100)
    time.sleep(0.5)

    pwm_s.duty(1.75/20*100)
    time.sleep(0.5)
    img = img.copy(roi=(88, 29, 560, 432))
    img.draw_string_advanced(50, 50, 20, "fps: {}".format(clock.fps()), color=(255, 0, 0))
    img.compressed_for_ide()
    Display.show_image(img)
